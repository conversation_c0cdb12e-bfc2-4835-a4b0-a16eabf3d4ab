/**
 * Task Form Core Module
 * Main orchestrator for the task form system with separation of concerns
 * Handles core form functionality and coordinates with other modules
 */

class TaskFormCore {
    constructor() {
        // Core state
        this.initialized = false;

        // Module references (will be injected)
        this.templateManager = null;
        this.keyboardShortcuts = null;
        this.smartSuggestions = null;
        this.aiEnhancements = null;
        this.analyticsTracker = null;
        this.formValidator = null;
        this.uiNotifications = null;
        this.bulkOperations = null;
        this.recentTasks = null;

        // Core form elements cache
        this.formElements = {};

        this.init();
    }

    async init() {
        if (this.initialized) return;

        try {
            this.cacheFormElements();
            this.setupBasicFormHandlers();
            this.setDefaultDate();
            this.setupCategoryMapping();

            this.initialized = true;
            console.log('TaskFormCore initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskFormCore:', error);
        }
    }

    // Module injection methods for dependency management
    injectTemplateManager(templateManager) {
        this.templateManager = templateManager;
        templateManager.setCore(this);
    }

    injectKeyboardShortcuts(keyboardShortcuts) {
        this.keyboardShortcuts = keyboardShortcuts;
        keyboardShortcuts.setCore(this);
    }

    injectSmartSuggestions(smartSuggestions) {
        this.smartSuggestions = smartSuggestions;
        smartSuggestions.setCore(this);
    }

    injectAIEnhancements(aiEnhancements) {
        this.aiEnhancements = aiEnhancements;
        aiEnhancements.setCore(this);
    }

    injectAnalyticsTracker(analyticsTracker) {
        this.analyticsTracker = analyticsTracker;
        if (analyticsTracker) {
            // Track form initialization
            analyticsTracker.trackFormStart();
        }
    }

    injectFormValidation(formValidation) {
        this.formValidation = formValidation;
        if (formValidation) {
            formValidation.setCore(this);
        }
    }

    injectUINotifications(uiNotifications) {
        this.uiNotifications = uiNotifications;
        if (uiNotifications) {
            uiNotifications.setCore(this);
        }
    }

    injectBulkOperations(bulkOperations) {
        this.bulkOperations = bulkOperations;
        bulkOperations.setCore(this);
    }

    injectRecentTasks(recentTasks) {
        this.recentTasks = recentTasks;
        recentTasks.setCore(this);
    }

    // Core form element caching
    cacheFormElements() {
        this.formElements = {
            form: document.querySelector('form[method="POST"]'),
            title: document.querySelector('[name="title"]'),
            classification: document.querySelector('[name="classification"]'),
            description: document.querySelector('[name="description"]'),
            estTime: document.querySelector('[name="est_time"]'),
            category: document.querySelector('[name="category"]'),
            date: document.querySelector('[name="date"]')
        };
    }

    // Get cached form elements
    getFormElement(name) {
        return this.formElements[name];
    }

    // Get all form elements
    getFormElements() {
        return this.formElements;
    }

    // Basic form setup
    setupBasicFormHandlers() {
        const classificationSelect = this.getFormElement('classification');
        if (classificationSelect) {
            classificationSelect.addEventListener('change', () => {
                this.updateCategoryFromClassification();
            });
        }
    }

    // Set default date to today
    setDefaultDate() {
        const dateField = this.getFormElement('date');
        if (dateField && !dateField.value) {
            dateField.value = new Date().toISOString().split('T')[0];
        }
    }

    // Category auto-update based on classification
    updateCategoryFromClassification() {
        const classificationSelect = this.getFormElement('classification');
        const categoryField = this.getFormElement('category');

        if (!classificationSelect || !categoryField) return;

        const classification = classificationSelect.value;

        // Category mapping (should match backend config)
        const categoryMap = {
            'Planning': 'Adhoc',
            'Offline Processing': 'Adhoc',
            'Execution': 'Adhoc',
            'Business Support Activities': 'Business Support Activities',
            'Operational Project Involvement': 'Adhoc'
        };

        const category = categoryMap[classification] || '';
        categoryField.value = category;
    }

    // Setup category mapping listener
    setupCategoryMapping() {
        const classificationSelect = this.getFormElement('classification');
        if (classificationSelect) {
            classificationSelect.addEventListener('change', () => {
                this.updateCategoryFromClassification();
            });
        }
    }

    // Apply template data to form (called by template manager)
    applyTemplateData(templateData, templateName = '') {
        const form = this.getFormElement('form');
        if (!form) return;

        // Show visual feedback if UI notifications are available
        if (this.uiNotifications && templateName) {
            this.uiNotifications.showTemplateAppliedFeedback(templateName);
        }

        // Animate form fields as they're filled
        Object.entries(templateData).forEach(([field, value], index) => {
            setTimeout(() => {
                this.animateFieldUpdate(field, value);
            }, index * 100);
        });

        // Auto-update category based on classification
        setTimeout(() => {
            this.updateCategoryFromClassification();
        }, 300);
    }

    // Animate field updates with visual feedback
    animateFieldUpdate(fieldName, value) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        // Add highlight animation
        field.style.transition = 'all 0.3s ease';
        field.style.backgroundColor = '#e3f2fd';
        field.style.transform = 'scale(1.02)';

        // Set value
        field.value = value;

        // Trigger change event for any listeners
        field.dispatchEvent(new Event('change', { bubbles: true }));

        // Remove animation after delay
        setTimeout(() => {
            field.style.backgroundColor = '';
            field.style.transform = '';
        }, 600);
    }

    // Clear form fields
    clearForm() {
        const form = this.getFormElement('form');
        if (!form) return;

        // Clear all input fields except date (keep today's date)
        const inputs = form.querySelectorAll('input:not([name="date"]), textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        // Clear any suggestions if smart suggestions module is available
        if (this.smartSuggestions) {
            this.smartSuggestions.closeAllSuggestions();
        }

        // Focus on title field
        const titleField = this.getFormElement('title');
        if (titleField) {
            titleField.focus();
        }

        // Show notification if UI notifications are available
        if (this.uiNotifications) {
            this.uiNotifications.showNotification('Form cleared', 'info', 2000);
        }
    }

    // Submit form with validation
    submitForm() {
        const form = this.getFormElement('form');
        if (!form) return false;

        // Validate form if validator is available
        if (this.formValidator && !this.formValidator.validateForm()) {
            return false;
        }

        // Submit the form
        form.submit();
        return true;
    }

    // Get form data as object
    getFormData() {
        const formData = {};
        const form = this.getFormElement('form');
        if (!form) return formData;

        const formElements = new FormData(form);
        for (const [key, value] of formElements.entries()) {
            formData[key] = value;
        }

        return formData;
    }

    // Set form data from object
    setFormData(data) {
        Object.entries(data).forEach(([field, value]) => {
            const element = document.querySelector(`[name="${field}"]`);
            if (element) {
                element.value = value;
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        });
    }

    // Check if form has unsaved changes
    hasUnsavedChanges() {
        const form = this.getFormElement('form');
        if (!form) return false;

        const inputs = form.querySelectorAll('input, textarea, select');
        for (const input of inputs) {
            if (input.name === 'date') continue; // Skip date field
            if (input.value && input.value.trim()) {
                return true;
            }
        }
        return false;
    }

    // Get current form state for analytics
    getFormState() {
        const formData = this.getFormData();
        return {
            hasTitle: !!(formData.title && formData.title.trim()),
            hasClassification: !!(formData.classification),
            hasDescription: !!(formData.description && formData.description.trim()),
            hasEstTime: !!(formData.est_time),
            hasCategory: !!(formData.category),
            fieldCount: Object.keys(formData).filter(key => formData[key] && formData[key].toString().trim()).length
        };
    }

    // Destroy and cleanup
    destroy() {
        // Clean up event listeners and references
        this.formElements = {};
        this.initialized = false;

        // Notify modules to cleanup
        if (this.templateManager) this.templateManager.destroy();
        if (this.keyboardShortcuts) this.keyboardShortcuts.destroy();
        if (this.smartSuggestions) this.smartSuggestions.destroy();
        if (this.aiEnhancements) this.aiEnhancements.destroy();
        if (this.formValidator) this.formValidator.destroy();
        if (this.bulkOperations) this.bulkOperations.destroy();
        if (this.recentTasks) this.recentTasks.destroy();
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskFormCore;
} else {
    window.TaskFormCore = TaskFormCore;
}
