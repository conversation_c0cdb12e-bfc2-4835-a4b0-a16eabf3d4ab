/**
 * Task Analytics Tracker Module
 * Handles analytics tracking and performance monitoring for user interactions
 */

class TaskAnalyticsTracker {
    constructor() {
        this.sessionStart = Date.now();
        this.eventQueue = [];
        this.formStartTime = null;
        this.fieldInteractions = new Map();
        this.batchSize = 10;
        this.flushInterval = 30000; // 30 seconds
        
        this.startPeriodicFlush();
    }

    // Track a user interaction event
    trackEvent(eventType, data = {}) {
        const event = {
            event_type: eventType,
            data: {
                ...data,
                timestamp: new Date().toISOString(),
                session_duration: Date.now() - this.sessionStart,
                page_url: window.location.href,
                user_agent: navigator.userAgent
            },
            user_id: this.getUserId()
        };

        this.eventQueue.push(event);

        // Auto-flush if queue is getting large
        if (this.eventQueue.length >= this.batchSize) {
            this.flushEvents();
        }
    }

    // Get user ID (can be enhanced with actual user identification)
    getUserId() {
        // Try to get from session storage first
        let userId = sessionStorage.getItem('analytics_user_id');
        if (!userId) {
            // Generate a session-based ID
            userId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('analytics_user_id', userId);
        }
        return userId;
    }

    // Send events to the server
    async flushEvents() {
        if (this.eventQueue.length === 0) return;

        const eventsToSend = [...this.eventQueue];
        this.eventQueue = [];

        try {
            // Send events in batches to avoid overwhelming the server
            const batchPromises = [];
            for (let i = 0; i < eventsToSend.length; i += this.batchSize) {
                const batch = eventsToSend.slice(i, i + this.batchSize);
                batchPromises.push(this.sendEventBatch(batch));
            }

            await Promise.all(batchPromises);
        } catch (error) {
            console.error('Error sending analytics events:', error);
            // Re-queue events on failure (but limit to prevent memory issues)
            if (this.eventQueue.length < 100) {
                this.eventQueue.unshift(...eventsToSend);
            }
        }
    }

    // Send a batch of events
    async sendEventBatch(events) {
        const response = await fetch('/api/analytics/track-events', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ events })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }

    // Start periodic flushing of events
    startPeriodicFlush() {
        this.flushTimer = setInterval(() => {
            this.flushEvents();
        }, this.flushInterval);

        // Also flush on page unload
        window.addEventListener('beforeunload', () => {
            this.flushEvents();
        });
    }

    // Track when user starts filling the form
    trackFormStart() {
        this.formStartTime = Date.now();
        this.trackEvent('form_started', {
            page_url: window.location.href,
            form_type: 'task_creation'
        });
    }

    // Track form completion
    trackFormCompletion(fieldsUsed, errorsEncountered = 0) {
        if (!this.formStartTime) return;

        const completionTime = (Date.now() - this.formStartTime) / 1000; // seconds

        this.trackEvent('form_completed', {
            completion_time: completionTime,
            fields_used: fieldsUsed,
            errors_encountered: errorsEncountered,
            total_fields: fieldsUsed.length,
            field_interactions: this.getFieldInteractionSummary()
        });

        // Reset form timing
        this.formStartTime = null;
    }

    // Track form abandonment
    trackFormAbandonment(reason = 'unknown') {
        if (!this.formStartTime) return;

        const timeSpent = (Date.now() - this.formStartTime) / 1000;

        this.trackEvent('form_abandoned', {
            time_spent: timeSpent,
            reason: reason,
            fields_filled: this.getFilledFieldsCount(),
            field_interactions: this.getFieldInteractionSummary()
        });

        this.formStartTime = null;
    }

    // Track when a suggestion is shown
    trackSuggestionShown(suggestionType, suggestionData, confidence = 0) {
        this.trackEvent('suggestion_shown', {
            suggestion_type: suggestionType,
            confidence: confidence,
            suggestion_data: suggestionData,
            context: this.getCurrentFormContext()
        });
    }

    // Track user interaction with suggestions
    async trackSuggestionInteraction(interactionType, suggestionType, suggestionData, responseTime = 0) {
        try {
            // Send immediately for suggestion interactions (important for ML training)
            await fetch('/api/suggestions/track-interaction', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    type: interactionType, // 'accepted', 'rejected', 'ignored'
                    suggestion_type: suggestionType,
                    suggestion_data: suggestionData,
                    response_time: responseTime,
                    context: this.getCurrentFormContext(),
                    timestamp: new Date().toISOString(),
                    user_id: this.getUserId()
                })
            });

            // Also track in general analytics
            this.trackEvent('suggestion_interaction', {
                interaction_type: interactionType,
                suggestion_type: suggestionType,
                response_time: responseTime
            });
        } catch (error) {
            console.error('Error tracking suggestion interaction:', error);
        }
    }

    // Track performance metrics
    trackPerformanceMetric(metricName, value, context = {}) {
        this.trackEvent('performance_metric', {
            metric_name: metricName,
            value: value,
            context: context,
            performance_timing: this.getPerformanceTiming()
        });
    }

    // Track field-level interactions
    trackFieldInteraction(fieldName, action, value = null) {
        const fieldKey = fieldName;

        if (!this.fieldInteractions.has(fieldKey)) {
            this.fieldInteractions.set(fieldKey, {
                focus_count: 0,
                change_count: 0,
                first_focus: Date.now(),
                total_focus_time: 0,
                last_focus_start: null,
                value_changes: []
            });
        }

        const field = this.fieldInteractions.get(fieldKey);
        const now = Date.now();

        switch (action) {
            case 'focus':
                field.focus_count++;
                field.last_focus_start = now;
                if (field.focus_count === 1) {
                    field.first_focus = now;
                }
                break;
            case 'blur':
                if (field.last_focus_start) {
                    field.total_focus_time += now - field.last_focus_start;
                    field.last_focus_start = null;
                }
                break;
            case 'change':
                field.change_count++;
                if (value !== null) {
                    field.value_changes.push({
                        timestamp: now,
                        value: value,
                        length: value.toString().length
                    });
                }
                break;
        }

        this.fieldInteractions.set(fieldKey, field);

        // Track significant field interactions
        if (action === 'change' && field.change_count % 5 === 0) {
            this.trackEvent('field_interaction_milestone', {
                field_name: fieldName,
                change_count: field.change_count,
                focus_count: field.focus_count,
                total_focus_time: field.total_focus_time
            });
        }
    }

    // Track template usage
    trackTemplateUsage(templateId, templateName, isCustom = false) {
        this.trackEvent('template_applied', {
            template_id: templateId,
            template_name: templateName,
            is_custom: isCustom,
            context: this.getCurrentFormContext()
        });
    }

    // Track keyboard shortcut usage
    trackKeyboardShortcut(shortcut, action) {
        this.trackEvent('keyboard_shortcut_used', {
            shortcut: shortcut,
            action: action,
            context: this.getCurrentFormContext()
        });
    }

    // Track AI feature usage
    trackAIFeatureUsage(featureType, data = {}) {
        this.trackEvent('ai_feature_used', {
            feature_type: featureType,
            ...data,
            context: this.getCurrentFormContext()
        });
    }

    // Track error occurrences
    trackError(errorType, errorMessage, context = {}) {
        this.trackEvent('error_occurred', {
            error_type: errorType,
            error_message: errorMessage,
            context: context,
            stack_trace: new Error().stack
        });
    }

    // Get summary of field interactions
    getFieldInteractionSummary() {
        const summary = {};

        for (const [fieldName, data] of this.fieldInteractions) {
            summary[fieldName] = {
                focus_count: data.focus_count,
                change_count: data.change_count,
                total_focus_time: data.total_focus_time,
                avg_focus_time: data.focus_count > 0 ? data.total_focus_time / data.focus_count : 0,
                value_changes_count: data.value_changes.length,
                final_value_length: data.value_changes.length > 0 ? 
                    data.value_changes[data.value_changes.length - 1].length : 0
            };
        }

        return summary;
    }

    // Get current form context
    getCurrentFormContext() {
        const context = {};
        
        try {
            const titleField = document.querySelector('[name="title"]');
            const classificationField = document.querySelector('[name="classification"]');
            const descriptionField = document.querySelector('[name="description"]');
            const estTimeField = document.querySelector('[name="est_time"]');

            context.title_length = titleField ? titleField.value.length : 0;
            context.description_length = descriptionField ? descriptionField.value.length : 0;
            context.has_classification = !!(classificationField && classificationField.value);
            context.has_est_time = !!(estTimeField && estTimeField.value);
            context.form_completion_percentage = this.calculateFormCompletionPercentage();
        } catch (error) {
            console.warn('Error getting form context:', error);
        }

        return context;
    }

    // Calculate form completion percentage
    calculateFormCompletionPercentage() {
        const requiredFields = ['title', 'classification', 'description', 'est_time'];
        let filledFields = 0;

        requiredFields.forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field && field.value && field.value.trim()) {
                filledFields++;
            }
        });

        return (filledFields / requiredFields.length) * 100;
    }

    // Get count of filled fields
    getFilledFieldsCount() {
        const fields = document.querySelectorAll('input, textarea, select');
        let count = 0;

        fields.forEach(field => {
            if (field.value && field.value.trim()) {
                count++;
            }
        });

        return count;
    }

    // Get performance timing data
    getPerformanceTiming() {
        if (!window.performance || !window.performance.timing) {
            return {};
        }

        const timing = window.performance.timing;
        return {
            page_load_time: timing.loadEventEnd - timing.navigationStart,
            dom_ready_time: timing.domContentLoadedEventEnd - timing.navigationStart,
            dns_lookup_time: timing.domainLookupEnd - timing.domainLookupStart,
            tcp_connect_time: timing.connectEnd - timing.connectStart
        };
    }

    // Track page visibility changes
    trackPageVisibility() {
        let visibilityStart = Date.now();
        let isVisible = !document.hidden;

        document.addEventListener('visibilitychange', () => {
            const now = Date.now();
            const timeSpent = now - visibilityStart;

            this.trackEvent('page_visibility_change', {
                was_visible: isVisible,
                time_spent: timeSpent,
                new_visibility_state: document.hidden ? 'hidden' : 'visible'
            });

            isVisible = !document.hidden;
            visibilityStart = now;
        });
    }

    // Get analytics summary for debugging
    getAnalyticsSummary() {
        return {
            session_duration: Date.now() - this.sessionStart,
            events_queued: this.eventQueue.length,
            field_interactions: this.fieldInteractions.size,
            form_start_time: this.formStartTime,
            user_id: this.getUserId()
        };
    }

    // Clear field interaction data
    clearFieldInteractions() {
        this.fieldInteractions.clear();
    }

    // Stop tracking and cleanup
    destroy() {
        // Flush remaining events
        this.flushEvents();

        // Clear timers
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
        }

        // Clear data
        this.eventQueue = [];
        this.fieldInteractions.clear();
        this.formStartTime = null;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskAnalyticsTracker;
} else {
    window.TaskAnalyticsTracker = TaskAnalyticsTracker;
}
