/**
 * Task Keyboard Shortcuts Module
 * Handles keyboard shortcuts, command palette, and quick task functionality
 */

class TaskKeyboardShortcuts {
    constructor() {
        this.core = null;
        this.commandPaletteVisible = false;
        this.quickTaskModalVisible = false;
        this.shortcuts = {};
        
        this.initializeShortcuts();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;
        
        try {
            this.setupKeyboardListeners();
            this.setupCommandPalette();
            this.setupQuickTaskModal();
            this.setupTabNavigation();
            console.log('TaskKeyboardShortcuts initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskKeyboardShortcuts:', error);
        }
    }

    // Initialize keyboard shortcuts configuration
    initializeShortcuts() {
        this.shortcuts = {
            // Form actions
            'ctrl+enter': () => this.submitForm(),
            'ctrl+shift+c': () => this.clearForm(),
            'ctrl+shift+t': () => this.showTemplateSelector(),
            
            // Navigation
            'ctrl+k': () => this.toggleCommandPalette(),
            'ctrl+shift+q': () => this.toggleQuickTaskModal(),
            'escape': () => this.handleEscape(),
            
            // Field navigation
            'ctrl+1': () => this.focusField('title'),
            'ctrl+2': () => this.focusField('classification'),
            'ctrl+3': () => this.focusField('description'),
            'ctrl+4': () => this.focusField('est_time'),
            
            // Quick actions
            'ctrl+shift+h': () => this.showShortcutsHelp(),
            'ctrl+shift+r': () => this.showRecentTasks(),
            'ctrl+shift+f': () => this.showFavorites()
        };
    }

    // Setup main keyboard event listeners
    setupKeyboardListeners() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        // Prevent default browser shortcuts that conflict
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                const key = e.key.toLowerCase();
                if (['k', 'enter'].includes(key) && (e.ctrlKey || e.metaKey)) {
                    if (key === 'k' || (key === 'enter' && e.ctrlKey)) {
                        e.preventDefault();
                    }
                }
            }
        });
    }

    // Handle keyboard events
    handleKeyDown(e) {
        // Don't handle shortcuts if user is typing in an input field (except for specific shortcuts)
        const activeElement = document.activeElement;
        const isInputField = activeElement && (
            activeElement.tagName === 'INPUT' || 
            activeElement.tagName === 'TEXTAREA' || 
            activeElement.tagName === 'SELECT' ||
            activeElement.contentEditable === 'true'
        );

        // Build shortcut key string
        const parts = [];
        if (e.ctrlKey || e.metaKey) parts.push('ctrl');
        if (e.shiftKey) parts.push('shift');
        if (e.altKey) parts.push('alt');
        parts.push(e.key.toLowerCase());
        
        const shortcutKey = parts.join('+');

        // Handle escape key specially
        if (e.key === 'Escape') {
            this.handleEscape();
            return;
        }

        // Handle command palette shortcut even in input fields
        if (shortcutKey === 'ctrl+k') {
            e.preventDefault();
            this.toggleCommandPalette();
            return;
        }

        // Handle form submission shortcut in input fields
        if (shortcutKey === 'ctrl+enter' && isInputField) {
            e.preventDefault();
            this.submitForm();
            return;
        }

        // Skip other shortcuts if in input field
        if (isInputField) return;

        // Execute shortcut if it exists
        if (this.shortcuts[shortcutKey]) {
            e.preventDefault();
            this.shortcuts[shortcutKey]();
        }
    }

    // Handle escape key
    handleEscape() {
        if (this.commandPaletteVisible) {
            this.hideCommandPalette();
        } else if (this.quickTaskModalVisible) {
            this.hideQuickTaskModal();
        } else {
            // Close any open modals
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            });
        }
    }

    // Setup command palette
    setupCommandPalette() {
        const commandPalette = document.getElementById('commandPalette');
        if (!commandPalette) return;

        const commandInput = document.getElementById('commandInput');
        const commandResults = document.getElementById('commandResults');

        if (commandInput) {
            commandInput.addEventListener('input', (e) => {
                this.filterCommands(e.target.value);
            });

            commandInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    this.executeSelectedCommand();
                } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    this.navigateCommands(e.key === 'ArrowDown' ? 1 : -1);
                }
            });
        }
    }

    // Toggle command palette visibility
    toggleCommandPalette() {
        if (this.commandPaletteVisible) {
            this.hideCommandPalette();
        } else {
            this.showCommandPalette();
        }
    }

    // Show command palette
    showCommandPalette() {
        const commandPalette = document.getElementById('commandPalette');
        if (!commandPalette) return;

        commandPalette.style.display = 'block';
        this.commandPaletteVisible = true;

        // Focus on command input
        const commandInput = document.getElementById('commandInput');
        if (commandInput) {
            commandInput.focus();
            commandInput.value = '';
        }

        // Load available commands
        this.loadCommands();

        // Track usage if analytics is available
        if (this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('command_palette_opened');
        }
    }

    // Hide command palette
    hideCommandPalette() {
        const commandPalette = document.getElementById('commandPalette');
        if (commandPalette) {
            commandPalette.style.display = 'none';
        }
        this.commandPaletteVisible = false;
    }

    // Load available commands
    loadCommands() {
        const commands = [
            { id: 'clear-form', name: 'Clear Form', description: 'Clear all form fields', icon: 'bi-eraser' },
            { id: 'submit-form', name: 'Submit Form', description: 'Submit the current form', icon: 'bi-check-circle' },
            { id: 'show-templates', name: 'Show Templates', description: 'Open template selector', icon: 'bi-bookmark' },
            { id: 'show-recent', name: 'Recent Tasks', description: 'Show recent tasks', icon: 'bi-clock-history' },
            { id: 'show-favorites', name: 'Favorites', description: 'Show favorite tasks', icon: 'bi-star' },
            { id: 'quick-task', name: 'Quick Task', description: 'Open quick task modal', icon: 'bi-lightning' },
            { id: 'shortcuts-help', name: 'Keyboard Shortcuts', description: 'Show keyboard shortcuts help', icon: 'bi-keyboard' },
            { id: 'bulk-entry', name: 'Bulk Entry', description: 'Open bulk task entry', icon: 'bi-list-ul' }
        ];

        // Add template commands
        if (this.core.templateManager) {
            const templates = this.core.templateManager.getAllTemplates();
            templates.forEach(template => {
                commands.push({
                    id: `template-${template.id}`,
                    name: `Apply ${template.name}`,
                    description: `Apply ${template.name} template`,
                    icon: template.icon || 'bi-bookmark',
                    action: () => this.core.templateManager.applyTemplate(template)
                });
            });
        }

        this.availableCommands = commands;
        this.displayCommands(commands);
    }

    // Filter commands based on search
    filterCommands(query) {
        if (!this.availableCommands) return;

        const filtered = this.availableCommands.filter(cmd => 
            cmd.name.toLowerCase().includes(query.toLowerCase()) ||
            cmd.description.toLowerCase().includes(query.toLowerCase())
        );

        this.displayCommands(filtered);
    }

    // Display commands in the palette
    displayCommands(commands) {
        const commandResults = document.getElementById('commandResults');
        if (!commandResults) return;

        if (commands.length === 0) {
            commandResults.innerHTML = '<div class="command-item text-muted">No commands found</div>';
            return;
        }

        const html = commands.map((cmd, index) => `
            <div class="command-item ${index === 0 ? 'selected' : ''}" data-command-id="${cmd.id}">
                <i class="${cmd.icon} me-2"></i>
                <div class="command-content">
                    <div class="command-name">${cmd.name}</div>
                    <div class="command-description">${cmd.description}</div>
                </div>
            </div>
        `).join('');

        commandResults.innerHTML = html;

        // Add click handlers
        commandResults.querySelectorAll('.command-item').forEach(item => {
            item.addEventListener('click', () => {
                const commandId = item.dataset.commandId;
                this.executeCommand(commandId);
            });
        });
    }

    // Navigate through commands with arrow keys
    navigateCommands(direction) {
        const commandItems = document.querySelectorAll('.command-item');
        const currentSelected = document.querySelector('.command-item.selected');
        
        if (!commandItems.length) return;

        let currentIndex = 0;
        if (currentSelected) {
            currentIndex = Array.from(commandItems).indexOf(currentSelected);
            currentSelected.classList.remove('selected');
        }

        const newIndex = Math.max(0, Math.min(commandItems.length - 1, currentIndex + direction));
        commandItems[newIndex].classList.add('selected');
    }

    // Execute selected command
    executeSelectedCommand() {
        const selectedCommand = document.querySelector('.command-item.selected');
        if (selectedCommand) {
            const commandId = selectedCommand.dataset.commandId;
            this.executeCommand(commandId);
        }
    }

    // Execute a command by ID
    executeCommand(commandId) {
        this.hideCommandPalette();

        // Find command with custom action
        const command = this.availableCommands.find(cmd => cmd.id === commandId);
        if (command && command.action) {
            command.action();
            return;
        }

        // Handle built-in commands
        switch (commandId) {
            case 'clear-form':
                this.clearForm();
                break;
            case 'submit-form':
                this.submitForm();
                break;
            case 'show-templates':
                this.showTemplateSelector();
                break;
            case 'show-recent':
                this.showRecentTasks();
                break;
            case 'show-favorites':
                this.showFavorites();
                break;
            case 'quick-task':
                this.toggleQuickTaskModal();
                break;
            case 'shortcuts-help':
                this.showShortcutsHelp();
                break;
            case 'bulk-entry':
                this.showBulkEntry();
                break;
        }

        // Track command usage if analytics is available
        if (this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('command_executed', { command_id: commandId });
        }
    }

    // Setup quick task modal
    setupQuickTaskModal() {
        const quickTaskModal = document.getElementById('quickTaskModal');
        if (!quickTaskModal) return;

        quickTaskModal.addEventListener('shown.bs.modal', () => {
            this.quickTaskModalVisible = true;
            const titleInput = document.getElementById('quickTaskTitle');
            if (titleInput) {
                titleInput.focus();
            }
        });

        quickTaskModal.addEventListener('hidden.bs.modal', () => {
            this.quickTaskModalVisible = false;
        });
    }

    // Toggle quick task modal
    toggleQuickTaskModal() {
        const quickTaskModal = document.getElementById('quickTaskModal');
        if (!quickTaskModal) return;

        if (this.quickTaskModalVisible) {
            this.hideQuickTaskModal();
        } else {
            this.showQuickTaskModal();
        }
    }

    // Show quick task modal
    showQuickTaskModal() {
        const quickTaskModal = new bootstrap.Modal(document.getElementById('quickTaskModal'));
        quickTaskModal.show();
    }

    // Hide quick task modal
    hideQuickTaskModal() {
        const quickTaskModal = bootstrap.Modal.getInstance(document.getElementById('quickTaskModal'));
        if (quickTaskModal) {
            quickTaskModal.hide();
        }
    }

    // Setup enhanced tab navigation
    setupTabNavigation() {
        const formFields = ['title', 'classification', 'description', 'est_time'];
        
        formFields.forEach((fieldName, index) => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && !e.shiftKey && index < formFields.length - 1) {
                        // Enhanced tab behavior can be added here
                    }
                });
            }
        });
    }

    // Shortcut action methods
    submitForm() {
        if (this.core) {
            this.core.submitForm();
        }
    }

    clearForm() {
        if (this.core) {
            this.core.clearForm();
        }
    }

    focusField(fieldName) {
        const field = this.core ? this.core.getFormElement(fieldName) : document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.focus();
        }
    }

    showTemplateSelector() {
        // Trigger template dropdown or modal
        const templateButton = document.querySelector('[data-bs-target="#templateModal"]');
        if (templateButton) {
            templateButton.click();
        }
    }

    showRecentTasks() {
        if (this.core && this.core.recentTasks) {
            this.core.recentTasks.showRecentTasksModal();
        }
    }

    showFavorites() {
        if (this.core && this.core.recentTasks) {
            this.core.recentTasks.showFavoritesModal();
        }
    }

    showShortcutsHelp() {
        const helpModal = document.getElementById('shortcutsHelpModal');
        if (helpModal) {
            const modal = new bootstrap.Modal(helpModal);
            modal.show();
        }
    }

    showBulkEntry() {
        if (this.core && this.core.bulkOperations) {
            this.core.bulkOperations.showBulkEntryModal();
        }
    }

    // Get shortcuts for help display
    getShortcutsHelp() {
        return [
            { keys: 'Ctrl + Enter', description: 'Submit form' },
            { keys: 'Ctrl + Shift + C', description: 'Clear form' },
            { keys: 'Ctrl + K', description: 'Open command palette' },
            { keys: 'Ctrl + Shift + Q', description: 'Quick task modal' },
            { keys: 'Ctrl + Shift + T', description: 'Show templates' },
            { keys: 'Ctrl + 1-4', description: 'Focus form fields' },
            { keys: 'Ctrl + Shift + H', description: 'Show this help' },
            { keys: 'Ctrl + Shift + R', description: 'Recent tasks' },
            { keys: 'Ctrl + Shift + F', description: 'Favorites' },
            { keys: 'Escape', description: 'Close modals/palette' }
        ];
    }

    // Cleanup
    destroy() {
        document.removeEventListener('keydown', this.handleKeyDown);
        this.shortcuts = {};
        this.commandPaletteVisible = false;
        this.quickTaskModalVisible = false;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskKeyboardShortcuts;
} else {
    window.TaskKeyboardShortcuts = TaskKeyboardShortcuts;
}
