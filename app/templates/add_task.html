{% extends "base.html" %}

{% block title %}Add Task - AdhocLog{% endblock %}

{% block content %}
<!-- Include our custom CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/task_form.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-features.css') }}">

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Add New Task</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <!-- Task Form -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-form"></i> Task Details</h5>
                <button type="button" class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#bulkEntryModal" title="Create multiple tasks at once">
                    <i class="bi bi-stack"></i> Bulk Entry
                </button>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row g-3">
                        <!-- Title -->
                        <div class="col-12">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ task_data.get('title', '') }}" required
                                   placeholder="Enter task title...">
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="col-md-6">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="{{ task_data.get('date', '') }}">
                        </div>

                        <!-- Estimated Time -->
                        <div class="col-md-6">
                            <label for="est_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="est_time" name="est_time"
                                   value="{{ task_data.get('est_time', 30) }}" min="1" max="999"
                                   placeholder="30">
                        </div>

                        <!-- Classification -->
                        <div class="col-md-6">
                            <label for="classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if task_data.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>

                        <!-- Category (Auto-filled) -->
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="{{ task_data.get('category', '') }}" readonly>
                            <div class="form-text">Auto-filled based on classification</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Description / Actions Taken</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Describe what you did or plan to do...">{{ task_data.get('description', '') }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="form-actions d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Add Task
                                </button>
                                <a href="{{ url_for('task_list') }}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Add from Previous Tasks -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Add from Previous Tasks
                    <small class="text-muted ms-2">Click to auto-fill form</small>
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="quickAddContainer" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2 text-muted">Loading recent tasks...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Templates Section (moved to top) -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-start">
                <div>
                    <h5 class="mb-0">
                        <i class="bi bi-lightning-fill text-primary"></i> Quick Templates
                    </h5>
                    <small class="text-muted">Click to auto-fill form • Shortcuts: Ctrl+1-5</small>
                </div>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#customTemplateModal">
                        <i class="bi bi-bookmark-plus"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#manageTemplatesModal">
                        <i class="bi bi-gear"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="template-buttons-container" class="template-buttons-container">
                    <!-- Template buttons will be populated by JavaScript -->
                    <div class="text-center py-2">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading templates...</span>
                        </div>
                    </div>
                </div>

                <!-- Improved Template Instructions -->
                <div class="template-instructions">
                    <div class="instruction-card">
                        <div class="instruction-icon">
                            <i class="bi bi-clock-fill text-success"></i>
                        </div>
                        <div class="instruction-content">
                            <strong>Save time</strong>
                            <small class="text-muted">One-click templates</small>
                        </div>
                    </div>
                    <div class="instruction-card">
                        <div class="instruction-icon">
                            <i class="bi bi-keyboard-fill text-info"></i>
                        </div>
                        <div class="instruction-content">
                            <strong>Ctrl+1-5</strong>
                            <small class="text-muted">Quick access</small>
                        </div>
                    </div>
                    <div class="instruction-card">
                        <div class="instruction-icon">
                            <i class="bi bi-check-circle-fill text-primary"></i>
                        </div>
                        <div class="instruction-content">
                            <strong>Ctrl+Enter</strong>
                            <small class="text-muted">Submit form</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Classification Guide -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Classification Guide</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Classification</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Planning</strong></td>
                                <td>
                                    <small>
                                        • Team meetings<br>
                                        • Brainstorming sessions<br>
                                        • Coordination with other teams
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Offline Processing</strong></td>
                                <td>
                                    <small>
                                        • Research and investigation<br>
                                        • Creating Excel reports<br>
                                        • Drafting documentation<br>
                                        • Preparing presentations<br>
                                        • Scripting/Coding<br>
                                        • Training/Certification
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Execution</strong></td>
                                <td>
                                    <small>
                                        • Presenting to managers<br>
                                        • Health checks<br>
                                        • Client System/Technology Operations<br>
                                        • Attendance/PTO Reminder<br>
                                        • Client Engineering Daily Case Management
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Business Support Activities</strong></td>
                                <td>
                                    <small>
                                        • Attend Townhall and other activities<br>
                                        • 1-on-1 Meeting/Catch-Up<br>
                                        • Daily Standup<br>
                                        • Technical Ramblings<br>
                                        • Client Engineering Team Weekly Meeting<br>
                                        • Audience of a Presentation
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Operational Project Involvement</strong></td>
                                <td>
                                    <small>
                                        • Assisting colleagues with difficult issues<br>
                                        • Participating in high-priority issue/SRT<br>
                                        • Operation Request from US team<br>
                                        • Involvement on US Projects<br>
                                        • Change Management
                                    </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Template Creation Modal -->
<div class="modal fade" id="customTemplateModal" tabindex="-1" aria-labelledby="customTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customTemplateModalLabel">
                    <i class="bi bi-bookmark-plus"></i> Create Custom Template
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customTemplateForm" novalidate>
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="template-name" class="form-label">Template Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="template-name" name="name" required
                                   placeholder="My Custom Template">
                            <div class="invalid-feedback">
                                Please provide a template name.
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="template-title" class="form-label">Task Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="template-title" name="title" required
                                   placeholder="Task title template">
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="template-classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="template-classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}">{{ cls }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="template-time" class="form-label">Estimated Time (minutes) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="template-time" name="est_time" required
                                   min="1" max="999" placeholder="30">
                            <div class="invalid-feedback">
                                Please provide estimated time.
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="template-description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="template-description" name="description" rows="3" required
                                      placeholder="Template description..."></textarea>
                            <div class="invalid-feedback">
                                Please provide a description.
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCustomTemplate">
                    <i class="bi bi-check-lg"></i> Save Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Entry Modal -->
<div class="modal fade" id="bulkEntryModal" tabindex="-1" aria-labelledby="bulkEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkEntryModalLabel">
                    <i class="bi bi-list-ul"></i> Bulk Task Entry
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <p class="text-muted mb-3">
                        <i class="bi bi-info-circle"></i>
                        Add multiple tasks at once. Use the buttons below to add rows, or copy common fields.
                    </p>

                    <div class="d-flex gap-2 mb-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="addBulkRow">
                            <i class="bi bi-plus"></i> Add Row
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="copyFirstRow">
                            <i class="bi bi-copy"></i> Copy First Row
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clearAllRows">
                            <i class="bi bi-trash"></i> Clear All
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="bulkTasksTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 25%;">Title <span class="text-danger">*</span></th>
                                <th style="width: 20%;">Classification <span class="text-danger">*</span></th>
                                <th style="width: 35%;">Description <span class="text-danger">*</span></th>
                                <th style="width: 10%;">Time (min)</th>
                                <th style="width: 10%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="bulkTasksBody">
                            <!-- Bulk task rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>Tips:</strong>
                        <ul class="mb-0 mt-1">
                            <li>Fill out the first row completely, then use "Copy First Row" to replicate common fields</li>
                            <li>Only modify the fields that differ between tasks</li>
                            <li>Tasks with missing required fields will be skipped</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="me-auto">
                    <span class="text-muted" id="bulkTaskCount">0 tasks ready</span>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submitBulkTasks" disabled>
                    <i class="bi bi-check-lg"></i> Create All Tasks
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include our enhanced task form JavaScript -->
<script src="{{ url_for('static', filename='js/task_form.js') }}"></script>
<!-- Include AI Analysis Module -->
<script src="{{ url_for('static', filename='js/ai_analysis.js') }}"></script>
<!-- Include enhanced add task module -->
<script src="{{ url_for('static', filename='js/add_task_enhanced.js') }}"></script>
<script>
// Initialize TaskFormManager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing managers');

    if (window.TaskFormManager) {
        window.taskFormManager = new TaskFormManager();
        console.log('TaskFormManager initialized');
    }

    // Initialize AddTaskManager (this should be done here, not in the script file)
    if (window.AddTaskManager) {
        window.addTaskManager = new AddTaskManager();
        console.log('AddTaskManager initialized');

        // Initialize advanced features
        if (window.addTaskManager.initializeAdvancedFeatures) {
            window.addTaskManager.initializeAdvancedFeatures();
        }
    }
});
</script>
{% endblock %}

<!-- Template Management Modal -->
<div class="modal fade" id="manageTemplatesModal" tabindex="-1" aria-labelledby="manageTemplatesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="manageTemplatesModalLabel">
                    <i class="bi bi-gear"></i> Manage Custom Templates
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="templates-list-container">
                    <!-- Templates will be loaded dynamically -->
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading templates...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#customTemplateModal" data-bs-dismiss="modal">
                    <i class="bi bi-plus-circle"></i> Add New Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Template Confirmation Modal -->
<div class="modal fade" id="deleteTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Template Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to <strong>delete</strong> the template "<span id="deleteTemplateName"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteTemplate">
                    <i class="bi bi-trash"></i> Delete Template
                </button>
            </div>
        </div>
    </div>
</div>
